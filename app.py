from flask import Flask, render_template, send_from_directory, jsonify, request, redirect, url_for, send_file, flash
import pandas as pd
import qrcode
import os
import re
import json
from difflib import SequenceMatcher
from cryptography.fernet import Fernet
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO
from werkzeug.utils import secure_filename
import threading
import webview
import platform
from tempfile import NamedTemporaryFile
from reportlab.pdfgen import canvas
from reportlab.lib.utils import ImageReader
import subprocess
import traceback
from email_service import email_service
from google_oauth_service import google_oauth_service
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'your-secret-key-change-in-production')
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

QR_FOLDER = os.path.join(BASE_DIR, "static", "qr_codes")
DATASET_DIR = os.path.join(BASE_DIR, "participant_list")
TEMPLATE_DIR = os.path.join(BASE_DIR, "static", "id_templates")
CONFIG_PATH = os.path.join(BASE_DIR, "active_config.json")

os.makedirs(QR_FOLDER, exist_ok=True)
os.makedirs(DATASET_DIR, exist_ok=True)
os.makedirs(TEMPLATE_DIR, exist_ok=True)

with open("qr_key.key", "rb") as f:
    fernet = Fernet(f.read())

def load_active_config():
    if not os.path.exists(CONFIG_PATH):
        default_config = {
            "active_dataset": "sample_employees_dataset.csv",
            "active_template": "template1.png"
        }
        with open(CONFIG_PATH, "w") as f:
            json.dump(default_config, f)
        return default_config
    with open(CONFIG_PATH, "r") as f:
        return json.load(f)

def get_active_dataset():
    config = load_active_config()
    dataset_path = os.path.join(DATASET_DIR, config["active_dataset"])
    return pd.read_csv(dataset_path), config["active_template"]

def normalize_csv_headers(df):
    """Normalize CSV column headers to handle various naming conventions"""
    import re

    def normalize_column_name(col_name):
        """Normalize a single column name to standard format"""
        if not col_name or pd.isna(col_name):
            return col_name

        # Convert to string and strip whitespace
        col_str = str(col_name).strip()

        # Remove special characters and normalize spaces/underscores
        normalized = re.sub(r'[^\w\s-]', '', col_str)
        normalized = re.sub(r'[\s_-]+', ' ', normalized).strip().lower()

        # Define patterns for each required field
        id_patterns = ['id', 'identifier', 'emp id', 'employee id', 'staff id', 'worker id', 'number', 'no']
        name_patterns = ['name', 'full name', 'employee name', 'staff name', 'worker name', 'first name', 'fname']
        position_patterns = ['position', 'job title', 'title', 'role', 'designation', 'job', 'post']
        company_patterns = ['company', 'organization', 'org', 'employer', 'business', 'corporation', 'corp']
        email_patterns = [
            'email', 'e mail', 'e-mail', 'mail', 'email address', 'e mail address', 'e-mail address',
            'electronic mail', 'contact email', 'work email', 'business email', 'personal email',
            'emailaddress', 'mailaddress', 'contact', 'correspondence'
        ]

        # Check patterns and return standard name
        if any(pattern in normalized for pattern in id_patterns):
            return 'ID'
        elif any(pattern in normalized for pattern in name_patterns):
            return 'Name'
        elif any(pattern in normalized for pattern in position_patterns):
            return 'Position'
        elif any(pattern in normalized for pattern in company_patterns):
            return 'Company'
        elif any(pattern in normalized for pattern in email_patterns):
            return 'Email'
        else:
            # Return original column name if no pattern matches
            return col_str

    # Apply normalization to all columns
    new_columns = []
    for col in df.columns:
        normalized_col = normalize_column_name(col)
        new_columns.append(normalized_col)

    df.columns = new_columns

    # Log the column mapping for debugging
    print("DEBUG: Column mapping:")
    for old, new in zip(df.columns, new_columns):
        if old != new:
            print(f"  '{old}' -> '{new}'")

    return df

def has_email_column(df):
    """Check if the dataset has an email column"""
    has_col = 'Email' in df.columns
    not_all_na = not df['Email'].isna().all() if has_col else False
    not_all_empty = not (df['Email'] == '').all() if has_col else False

    print(f"DEBUG has_email_column:")
    print(f"  - Has Email column: {has_col}")
    if has_col:
        print(f"  - Email column values: {df['Email'].head().tolist()}")
        print(f"  - Not all NaN: {not_all_na}")
        print(f"  - Not all empty: {not_all_empty}")
        print(f"  - Final result: {has_col and not_all_na and not_all_empty}")

    return has_col and not_all_na and not_all_empty

def cleanup_previous_data():
    """Clean up previous QR codes and related data"""
    try:
        # Remove all QR code files
        if os.path.exists(QR_FOLDER):
            for filename in os.listdir(QR_FOLDER):
                file_path = os.path.join(QR_FOLDER, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    print(f"Removed QR file: {filename}")

        # Remove preview images
        static_dir = os.path.join(BASE_DIR, "static")
        if os.path.exists(static_dir):
            for filename in os.listdir(static_dir):
                if filename.startswith("preview_") and filename.endswith(".png"):
                    file_path = os.path.join(static_dir, filename)
                    os.remove(file_path)
                    print(f"Removed preview file: {filename}")

        print("✅ Previous data cleaned up successfully")
    except Exception as e:
        print(f"⚠️ Error during cleanup: {str(e)}")

def update_encrypted_qr_column(df):
    if 'EncryptedQR' not in df.columns:
        df['EncryptedQR'] = df.apply(lambda row: fernet.encrypt(
            f"id={str(row['ID']).zfill(3)};name={row['Name']};position={row['Position']};company={row['Company']}".encode()
        ).decode(), axis=1)
        config = load_active_config()
        dataset_path = os.path.join(DATASET_DIR, config["active_dataset"])
        df.to_csv(dataset_path, index=False)

def generate_qrs(df):
    existing_ids = set()
    for _, row in df.iterrows():
        filename = f"{row['ID']}.png"
        file_path = os.path.join(QR_FOLDER, filename)
        existing_ids.add(filename)
        if not os.path.exists(file_path):
            qr_img = qrcode.make(row['EncryptedQR'])
            qr_img.save(file_path)
    for f in os.listdir(QR_FOLDER):
        if f not in existing_ids:
            os.remove(os.path.join(QR_FOLDER, f))

def safe_float(val, default):
    try:
        return float(val)
    except (TypeError, ValueError):
        return default

@app.route("/check_dataset_email", methods=["POST"])
def check_dataset_email():
    """Check if a dataset file has email column"""
    try:
        data = request.get_json()
        dataset_filename = data.get("dataset_filename")

        if not dataset_filename:
            return {"has_email": False}, 400

        # Find the actual file with timestamp prefix
        matching_files = [f for f in os.listdir(DATASET_DIR) if f.endswith(dataset_filename)]
        if matching_files:
            dataset_path = os.path.join(DATASET_DIR, matching_files[-1])
        else:
            dataset_path = os.path.join(DATASET_DIR, dataset_filename)

        if not os.path.exists(dataset_path):
            return {"has_email": False}, 404

        df = pd.read_csv(dataset_path)
        df = normalize_csv_headers(df)
        has_email = has_email_column(df)

        return {"has_email": has_email}

    except Exception as e:
        print(f"Error checking dataset email: {e}")
        return {"has_email": False}, 500

@app.route("/activate", methods=["POST"])
def activate():
    import time
    dataset_file = request.files.get("dataset_file")
    template_file = request.files.get("template_file")
    existing_dataset = request.form.get("existing_dataset")
    existing_template = request.form.get("existing_template")

    # Email configuration
    has_email_column = request.form.get("has_email_column") == "true"
    sender_email = request.form.get("sender_email", "")
    sender_password = request.form.get("sender_password", "")
    smtp_server = request.form.get("smtp_server", "smtp.gmail.com")
    smtp_port = request.form.get("smtp_port", "587")
    sender_name = request.form.get("sender_name", "ID QR System")

    config = load_active_config()
    timestamp = str(int(time.time()))

    # Handle dataset selection/upload
    if dataset_file and dataset_file.filename:
        dataset_filename = timestamp + "_" + secure_filename(dataset_file.filename)
        dataset_path = os.path.join(DATASET_DIR, dataset_filename)
        dataset_file.save(dataset_path)
        config["active_dataset"] = dataset_filename
    elif existing_dataset:
        # Find the actual file with timestamp prefix
        matching_files = [f for f in os.listdir(DATASET_DIR) if f.endswith(existing_dataset)]
        if matching_files:
            config["active_dataset"] = matching_files[-1]  # Use the most recent
        else:
            config["active_dataset"] = existing_dataset
        dataset_path = os.path.join(DATASET_DIR, config["active_dataset"])
    else:
        dataset_path = os.path.join(DATASET_DIR, config["active_dataset"])

    # Handle template selection/upload
    if template_file and template_file.filename:
        template_filename = timestamp + "_" + secure_filename(template_file.filename)
        template_path = os.path.join(TEMPLATE_DIR, template_filename)
        template_file.save(template_path)
        config["active_template"] = template_filename
    elif existing_template:
        # Find the actual file with timestamp prefix
        matching_files = [f for f in os.listdir(TEMPLATE_DIR) if f.endswith(existing_template)]
        if matching_files:
            config["active_template"] = matching_files[-1]  # Use the most recent
        else:
            config["active_template"] = existing_template
    with open(CONFIG_PATH, "w") as f:
        json.dump(config, f)
    try:
        df = pd.read_csv(dataset_path)
    except Exception as e:
        return f"Error reading CSV file: {str(e)}. Please ensure the file is a valid CSV format.", 400

    # Normalize column headers
    df = normalize_csv_headers(df)

    required_cols = {'ID', 'Name', 'Position', 'Company'}
    if not required_cols.issubset(df.columns):
        missing_cols = required_cols - set(df.columns)
        available_cols = list(df.columns)
        return f"Dataset is missing required columns: {', '.join(missing_cols)}. Available columns: {', '.join(available_cols)}. Please ensure your CSV has columns that can be mapped to ID, Name, Position, and Company.", 400

    # Check if Email column exists, if not add empty one
    if 'Email' not in df.columns:
        df['Email'] = ''

    # Clean up previous data if new dataset is uploaded
    if dataset_file:
        cleanup_previous_data()
    if 'EncryptedQR' not in df.columns:
        df['EncryptedQR'] = df.apply(lambda row: fernet.encrypt(
            f"id={str(row['ID']).zfill(3)};name={row['Name']};position={row['Position']};company={row['Company']}".encode()
        ).decode(), axis=1)
        df.to_csv(dataset_path, index=False)
    if dataset_file:
        for f in os.listdir(QR_FOLDER):
            os.remove(os.path.join(QR_FOLDER, f))
        generate_qrs(df)

    # Save email configuration if provided
    if has_email_column and sender_email and sender_password:
        try:
            email_service.update_config(
                smtp_server,
                int(smtp_port),
                True,  # use_tls
                sender_email,
                sender_password,
                sender_name
            )
            print("✅ Email configuration saved successfully")
        except Exception as e:
            print(f"⚠️ Error saving email configuration: {str(e)}")
    # Debug: Print the triggeredBy value
    triggered_by = request.form.get("triggeredBy")
    print(f"DEBUG: triggeredBy = {triggered_by}")
    print(f"DEBUG: Config saved with active_dataset = {config.get('active_dataset')}")
    print(f"DEBUG: Config saved with active_template = {config.get('active_template')}")

    if triggered_by == "first_run":
        print("DEBUG: Redirecting from first_run to index with success=true and from_setup=true")
        return redirect(url_for("index", success="true", from_setup="true"))
    elif triggered_by == "manual":
        print("DEBUG: Redirecting from manual to index with success=true")
        return redirect(url_for("index", success="true"))

    print("DEBUG: Redirecting to index without success parameter")
    return redirect(url_for("index"))

def is_first_run():
    """Check if this is the first run of the application"""
    # If we're coming from first_run setup, allow proceeding to index
    if request.args.get('from_setup') == 'true':
        print("DEBUG: from_setup=true detected, skipping first run check")
        return False

    # First check if config file exists
    if not os.path.exists(CONFIG_PATH):
        print("DEBUG: Config file doesn't exist, this is first run")
        return True

    try:
        with open(CONFIG_PATH, "r") as f:
            config = json.load(f)

        # Normal checks
        if "active_dataset" not in config or not config["active_dataset"]:
            print("DEBUG: No active dataset in config, this is first run")
            return True

        dataset_path = os.path.join(DATASET_DIR, config["active_dataset"])
        if not os.path.exists(dataset_path):
            print(f"DEBUG: Dataset file {dataset_path} doesn't exist, this is first run")
            return True

        if "active_template" not in config or not config["active_template"]:
            print("DEBUG: No active template in config, this is first run")
            return True

        template_path = os.path.join(TEMPLATE_DIR, config["active_template"])
        if not os.path.exists(template_path):
            print(f"DEBUG: Template file {template_path} doesn't exist, this is first run")
            return True

        print("DEBUG: All checks passed, not first run")
        return False

    except Exception as e:
        print(f"Error checking first run status: {e}")
        return True

@app.route("/")
def index():
    print("DEBUG: Index route called")
    # Check if this is first run
    if is_first_run():
        return redirect(url_for("first_run"))

    config = load_active_config()
    dataset_path = os.path.join(DATASET_DIR, config["active_dataset"])

    if not os.path.exists(dataset_path):
        return redirect(url_for("first_run"))

    df = pd.read_csv(dataset_path)

    # Normalize headers and check for email column
    df = normalize_csv_headers(df)
    if 'Email' not in df.columns:
        df['Email'] = ''
        df.to_csv(dataset_path, index=False)

    update_encrypted_qr_column(df)
    generate_qrs(df)
    success = request.args.get("success", "false") == "true"

    # Check if email functionality is available
    email_available = has_email_column(df)
    print(f"DEBUG: Rendering template with email_available = {email_available}")

    return render_template(
        "index.html",
        files=os.listdir(QR_FOLDER),
        success=success,
        active_template=config["active_template"],
        active_dataset=config["active_dataset"],
        email_available=email_available
    )

@app.route("/first_run")
def first_run():
    """First run setup page"""
    # Get existing datasets and templates
    dataset_files = []
    template_files = []

    if os.path.exists(DATASET_DIR):
        dataset_files = [f for f in os.listdir(DATASET_DIR) if f.endswith('.csv')]
        # Remove timestamped files from display, keep only base names
        unique_datasets = set()
        for f in dataset_files:
            if '_' in f and f.split('_', 1)[0].isdigit():
                # This is a timestamped file, get the base name
                base_name = '_'.join(f.split('_')[1:])
                unique_datasets.add(base_name)
            else:
                unique_datasets.add(f)
        dataset_files = sorted(list(unique_datasets))

    if os.path.exists(TEMPLATE_DIR):
        template_files = [f for f in os.listdir(TEMPLATE_DIR) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.webp', '.bmp', '.gif', '.tiff', '.tif'))]
        # Remove timestamped files from display, keep only base names
        unique_templates = set()
        for f in template_files:
            if '_' in f and f.split('_', 1)[0].isdigit():
                # This is a timestamped file, get the base name
                base_name = '_'.join(f.split('_')[1:])
                unique_templates.add(base_name)
            else:
                unique_templates.add(f)
        template_files = sorted(list(unique_templates))

    return render_template(
        "first_run.html",
        dataset_files=dataset_files,
        template_files=template_files
    )

@app.route("/download/<filename>")
def download_qr(filename):
    return send_from_directory(QR_FOLDER, filename, as_attachment=True)

def parse_qr_content(qr_content):
    try:
        decrypted = fernet.decrypt(qr_content.encode()).decode()
    except:
        return None
    try:
        if '=' in decrypted and ';' in decrypted:
            parts = dict(item.split('=', 1) for item in decrypted.split(';') if '=' in item)
            return parts.get('id', '').strip()
    except:
        return None
    return None

def find_employee_by_id(id_value):
    df, _ = get_active_dataset()
    exact_match = df[df['ID'].astype(str) == id_value]
    if not exact_match.empty:
        return exact_match.iloc[0]
    padded_id = id_value.zfill(3)
    padded_match = df[df['ID'].astype(str) == padded_id]
    if not padded_match.empty:
        return padded_match.iloc[0]
    stripped_id = id_value.lstrip('0')
    if stripped_id:
        stripped_match = df[df['ID'].astype(str) == stripped_id]
        if not stripped_match.empty:
            return stripped_match.iloc[0]
    best_match = None
    best_score = 0
    for _, row in df.iterrows():
        score = SequenceMatcher(None, str(row['ID']), id_value).ratio()
        if score > best_score and score > 0.8:
            best_score = score
            best_match = row
    return best_match

@app.route("/get_data", methods=["POST"])
def get_data():
    data = request.get_json()
    qr_content = data.get("qr_content", "")
    if not qr_content:
        return {"error": "No QR content provided"}, 400
    id_value = parse_qr_content(qr_content)
    if not id_value:
        return {"error": "Could not decrypt or parse ID", "qr_content": qr_content}, 400
    employee = find_employee_by_id(id_value)
    if employee is None:
        return {"error": f"Employee with ID '{id_value}' not found", "searched_id": id_value}, 404

    # Include email if available
    result = {
        "ID": str(employee['ID']),
        "Name": str(employee['Name']),
        "Position": str(employee['Position']),
        "Company": str(employee['Company']),
        "qr_content": qr_content,
        "parsed_id": id_value
    }

    # Add email if it exists and is not empty
    if 'Email' in employee and pd.notna(employee['Email']) and str(employee['Email']).strip():
        result["Email"] = str(employee['Email']).strip()

    return result

@app.route("/get_employee_by_id", methods=["POST"])
def get_employee_by_id():
    """Get employee data directly by ID (for manual selection)"""
    data = request.get_json()
    employee_id = data.get("employee_id", "")
    if not employee_id:
        return {"error": "No employee ID provided"}, 400

    try:
        # Normalize the ID (pad with zeros if needed)
        normalized_id = str(employee_id).zfill(3)
        employee = find_employee_by_id(normalized_id)
        if employee is None:
            return {"error": f"Employee with ID '{employee_id}' not found"}, 404

        # Return employee data
        result = {
            "ID": str(employee['ID']),
            "Name": str(employee['Name']),
            "Position": str(employee['Position']),
            "Company": str(employee['Company']),
            "qr_content": f"Manual selection: ID {employee_id}",
            "parsed_id": normalized_id
        }

        # Add email if it exists and is not empty
        if 'Email' in employee and str(employee['Email']).strip():
            result['Email'] = str(employee['Email'])

        return result

    except Exception as e:
        return {"error": f"Error retrieving employee: {str(e)}"}, 500

@app.route("/employees", methods=["GET"])
def get_employees():
    df, _ = get_active_dataset()
    return jsonify(df.to_dict('records'))

@app.route("/print")
def print_page():
    name = request.args.get("id_name", "")
    position = request.args.get("id_position", "")
    company = request.args.get("id_company", "")
    template_filename = request.args.get("template", "")
    if not template_filename:
        return "Missing template filename", 400
    template_url = url_for('static', filename=f"id_templates/{template_filename}")
    paper_size = request.args.get("paper_size", "A4")
    custom_width = request.args.get("custom_width", "")
    custom_height = request.args.get("custom_height", "")
    return render_template(
        "print.html",
        name=name,
        position=position,
        company=company,
        template_url=template_url,
        paper_size=paper_size,
        custom_width=custom_width,
        custom_height=custom_height,
    )

@app.route("/scan_camera")
def scan_camera():
    return render_template("camera_scanner.html")

@app.route("/preview_qr/<employee_id>")
def preview_qr(employee_id):
    df, template_filename = get_active_dataset()
    normalized_id = str(employee_id).zfill(3)
    employee = df[df['ID'].astype(str).str.zfill(3) == normalized_id]
    if employee.empty:
        return {"error": "Employee not found"}, 404
    emp = employee.iloc[0]
    encrypted_data = emp['EncryptedQR']
    qr_img = qrcode.make(encrypted_data).resize((150, 150))
    template_path = os.path.join(TEMPLATE_DIR, template_filename)
    if not os.path.exists(template_path):
        return {"error": "Template not found"}, 500
    id_template = Image.open(template_path).convert("RGBA")
    id_template.paste(qr_img, (400, 100))
    draw = ImageDraw.Draw(id_template)
    try:
        font_path = os.path.join(BASE_DIR, "static", "fonts", "arial.ttf")
        font = ImageFont.truetype(font_path, 18)
    except:
        font = ImageFont.load_default()
    draw.text((50, 100), f"Name: {emp['Name']}", fill="black", font=font)
    draw.text((50, 140), f"Position: {emp['Position']}", fill="black", font=font)
    draw.text((50, 180), f"Company: {emp['Company']}", fill="black", font=font)
    buffer = BytesIO()
    id_template.save(buffer, format="PNG")
    buffer.seek(0)
    return send_file(buffer, mimetype="image/png")

@app.route("/print_id", methods=["POST"])
def print_id():
    data = request.get_json()
    employee_id = data.get("id", "")
    df, template_filename = get_active_dataset()
    normalized_id = str(employee_id).zfill(3)
    employee = df[df['ID'].astype(str).str.zfill(3) == normalized_id]
    if employee.empty:
        return {"error": "Employee not found"}, 404
    emp = employee.iloc[0]
    template_path = os.path.join(TEMPLATE_DIR, template_filename)
    if not os.path.exists(template_path):
        return {"error": "Template not found"}, 500
    id_template = Image.open(template_path).convert("RGBA").resize((1013, 638))
    qr_img = qrcode.make(emp['EncryptedQR']).resize((150, 150))
    id_template.paste(qr_img, (400, 80))
    draw = ImageDraw.Draw(id_template)
    try:
        font_path = os.path.join(BASE_DIR, "static", "fonts", "arial.ttf")
        font = ImageFont.truetype(font_path, 24)
    except:
        font = ImageFont.load_default()
    fields = [
        ("Name", emp.get("Name", "N/A"), (50, 40)),
        ("Position", emp.get("Position", "N/A"), (50, 80)),
        ("Company", emp.get("Company", "N/A"), (50, 120)),
    ]
    for label, value, (x, y) in fields:
        text = f"{label}: {value}"
        draw.text((x, y), text, fill="black", font=font)
        text_width = draw.textlength(text, font=font)
        underline_y = y + font.size + 2
        draw.line((x, underline_y, x + text_width, underline_y), fill="black", width=1)
    with NamedTemporaryFile(delete=False, suffix=".png") as tmp_img:
        id_template.save(tmp_img.name)
        img_path = tmp_img.name
    with NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_pdf:
        pdf_path = tmp_pdf.name
        c = canvas.Canvas(pdf_path, pagesize=(1013, 638))
        c.drawImage(ImageReader(img_path), 0, 0, width=1013, height=638)
        c.save()
    try:
        if platform.system() == "Windows":
            os.startfile(pdf_path, "print")
        else:
            subprocess.run(["lp", pdf_path])
    except Exception as e:
        return {"error": f"Failed to print: {str(e)}"}, 500
    return {"success": True, "message": f"Print job sent for ID: {employee_id}"}

@app.route("/print_image_direct", methods=["POST"])
def print_image_direct():
    try:
        data = request.get_json()
        image_data = data.get("image_base64", "")
        paper_size = data.get("paper_size", "custom")
        custom_width = safe_float(data.get("custom_width"), 3.375)
        custom_height = safe_float(data.get("custom_height"), 2.125)
        redirect_to_preview = data.get("redirect", False)

        if not image_data.startswith("data:image/png;base64,"):
            return {"error": "Invalid image data format"}, 400

        import base64
        import io
        import uuid
        from tempfile import NamedTemporaryFile
        from PIL import Image

        dpi = 300

        paper_sizes = {
            "a4": (8.27, 11.69),
            "letter": (8.5, 11),
            "legal": (8.5, 14),
            "a3": (11.7, 16.5),
            "a5": (5.8, 8.3),
            "a6": (4.1, 5.8),
            "a7": (2.9, 3.9),
            "b5": (7.2, 10.1),
            "b4": (9.8, 13.9),
            "4x6": (4, 6),
            "5x7": (5, 7),
            "5x8": (5, 8),
            "9x13": (3.5, 5)
        }

        if paper_size.lower() != "custom":
            custom_width, custom_height = paper_sizes.get(paper_size.lower(), (3.375, 2.125))

        # Final paper pixel dimensions
        paper_width_px = int(custom_width * dpi)
        paper_height_px = int(custom_height * dpi)

        # Decode and open image
        base64_data = image_data.split(",")[1]
        img_bytes = base64.b64decode(base64_data)
        img = Image.open(io.BytesIO(img_bytes)).convert("RGB")

        # Scale image to fill the entire paper size (stretch)
        resized_img = img.resize((paper_width_px, paper_height_px), Image.LANCZOS)

        from flask import jsonify, url_for

        if redirect_to_preview:
            # Save resized image to static folder
            session_id = str(uuid.uuid4())
            file_path = f"static/preview_{session_id}.png"
            resized_img.save(file_path, dpi=(dpi, dpi))
            return jsonify({
                "success": True,
                "preview_url": url_for('show_preview', session_id=session_id, w=custom_width, h=custom_height)
            })

        # Save for direct printing
        with NamedTemporaryFile(delete=False, suffix=".png") as tmp_img:
            resized_img.save(tmp_img.name, dpi=(dpi, dpi))
            tmp_img_path = tmp_img.name

        import platform
        import subprocess
        if platform.system() == "Windows":
            subprocess.run(["mspaint", "/pt", tmp_img_path], check=True)
        elif platform.system() == "Darwin":
            subprocess.run(["lp", tmp_img_path], check=True)
        else:
            subprocess.run(["lpr", tmp_img_path], check=True)

        return {"success": True, "message": "Image sent to printer"}

    except Exception as e:
        import traceback
        traceback.print_exc()
        return {"error": f"Unexpected error: {str(e)}"}, 500

@app.route("/show_preview/<session_id>")
def show_preview(session_id):
    w = request.args.get("w", "8.27")
    h = request.args.get("h", "11.69")
    return render_template("preview.html", session_id=session_id, w=w, h=h)

@app.route("/send_qr_email", methods=["POST"])
def send_qr_email():
    """Send QR code via email"""
    try:
        data = request.get_json()
        employee_id = data.get("employee_id", "")
        recipient_email = data.get("email", "")

        if not employee_id or not recipient_email:
            return {"error": "Employee ID and email are required"}, 400

        # Get employee data
        df, _ = get_active_dataset()
        # Try both formats: simple number and zero-padded
        employee = df[df['ID'].astype(str) == str(employee_id)]

        if employee.empty:
            # Try zero-padded format
            normalized_id = str(employee_id).zfill(3)
            employee = df[df['ID'].astype(str).str.zfill(3) == normalized_id]

        if employee.empty:
            return {"error": f"Employee with ID {employee_id} not found"}, 404

        emp = employee.iloc[0]

        # Get QR code file path - use the actual ID from the dataset
        qr_filename = f"{emp['ID']}.png"
        qr_path = os.path.join(QR_FOLDER, qr_filename)

        if not os.path.exists(qr_path):
            return {"error": "QR code file not found"}, 404

        # Prepare employee data for email
        employee_data = {
            'ID': str(emp['ID']),
            'Name': str(emp['Name']),
            'Position': str(emp['Position']),
            'Company': str(emp['Company'])
        }

        # Check if email service is configured
        if not email_service.sender_email or not email_service.sender_password:
            return {"error": "Email not configured. Please configure email settings first."}, 400

        print(f"Sending QR email to {recipient_email} for employee {emp['Name']} (ID: {emp['ID']})")
        print(f"QR file path: {qr_path}")
        print(f"Email service config: {email_service.sender_email} via {email_service.smtp_server}:{email_service.smtp_port}")

        # Send email
        success, message = email_service.send_qr_email(
            recipient_email,
            str(emp['Name']),
            qr_path,
            employee_data
        )

        print(f"Email send result: success={success}, message='{message}'")

        if success:
            return {"success": True, "message": f"QR code sent to {recipient_email}"}
        else:
            return {"error": f"Failed to send email: {message}"}, 500

    except Exception as e:
        print(f"Exception in send_qr_email: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"error": f"Unexpected error: {str(e)}"}, 500

@app.route("/test_email_config", methods=["POST"])
def test_email_config():
    """Test email configuration"""
    try:
        # Check if email service has required credentials
        if not email_service.sender_email or not email_service.sender_password:
            return {"error": "Email credentials not configured. Please save configuration first."}, 400

        print(f"Testing email config: {email_service.sender_email} on {email_service.smtp_server}:{email_service.smtp_port}")
        success, message = email_service.test_connection()
        print(f"Test result: success={success}, message={message}")

        if success:
            return {"success": True, "message": "Email configuration is working"}
        else:
            # Provide more helpful error messages
            if "BadCredentials" in message:
                error_msg = "Invalid email credentials. For Gmail, please use an App Password instead of your regular password."
            elif "Authentication failed" in message:
                error_msg = "Authentication failed. Please verify your email and password are correct."
            elif "Connection" in message:
                error_msg = "Connection failed. Please check your internet connection and SMTP settings."
            else:
                error_msg = f"Email configuration failed: {message}"

            return {"error": error_msg}, 400
    except Exception as e:
        print(f"Email test exception: {str(e)}")
        return {"error": f"Test failed: {str(e)}"}, 500

@app.route("/update_email_config", methods=["POST"])
def update_email_config():
    """Update email configuration"""
    try:
        data = request.get_json()
        print(f"Received email config data: {data}")

        smtp_server = data.get('smtp_server', 'smtp.gmail.com')
        smtp_port = int(data.get('smtp_port', 587))
        use_tls = data.get('use_tls', True)
        sender_email = data.get('sender_email', '')
        sender_password = data.get('sender_password', '')
        sender_name = data.get('sender_name', 'ID QR System')

        if not sender_email or not sender_password:
            return {"error": "Sender email and password are required"}, 400

        print(f"Updating email config: {sender_email} on {smtp_server}:{smtp_port}")

        # Update email service configuration
        email_service.update_config(
            smtp_server, smtp_port, use_tls,
            sender_email, sender_password, sender_name
        )

        print(f"Email config updated successfully. Current config: {email_service.sender_email}")
        return {"success": True, "message": "Email configuration updated"}

    except Exception as e:
        print(f"Failed to update email config: {str(e)}")
        return {"error": f"Failed to update configuration: {str(e)}"}, 500

# Google OAuth2 Routes
@app.route("/google_auth_status")
def google_auth_status():
    """Get Google OAuth2 authentication status"""
    try:
        if not google_oauth_service.is_available():
            return {
                "available": False,
                "configured": False,
                "authenticated": False,
                "message": "Google APIs not installed. Install with: pip install google-auth google-auth-oauthlib google-api-python-client"
            }

        return {
            "available": True,
            "configured": google_oauth_service.is_configured(),
            "authenticated": google_oauth_service.is_authenticated(),
            "user_email": google_oauth_service.user_email,
            "user_name": google_oauth_service.user_name
        }
    except Exception as e:
        return {"error": str(e)}, 500

@app.route("/google_auth_url")
def google_auth_url():
    """Get Google OAuth2 authorization URL"""
    try:
        if not google_oauth_service.is_available():
            return {"error": "Google APIs not available"}, 400

        if not google_oauth_service.is_configured():
            return {"error": "OAuth2 not configured. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in environment variables."}, 400

        auth_url = google_oauth_service.get_auth_url()
        return {"auth_url": auth_url}
    except Exception as e:
        return {"error": str(e)}, 500

@app.route("/oauth2callback")
def oauth2callback():
    """Handle OAuth2 callback"""
    try:
        authorization_code = request.args.get('code')
        if not authorization_code:
            return "Authorization failed: No code received", 400

        success = google_oauth_service.handle_oauth_callback(authorization_code)
        if success:
            return """
            <html>
            <head><title>Authentication Successful</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                <h2 style="color: green;">✅ Authentication Successful!</h2>
                <p>You have successfully signed in with Google.</p>
                <p>You can now close this window and return to the application.</p>
                <script>
                    setTimeout(function() {
                        window.close();
                    }, 3000);
                </script>
            </body>
            </html>
            """
        else:
            return "Authentication failed", 400
    except Exception as e:
        return f"Authentication error: {str(e)}", 500

@app.route("/google_logout", methods=["POST"])
def google_logout():
    """Logout from Google OAuth2"""
    try:
        google_oauth_service.logout()
        return {"success": True, "message": "Logged out successfully"}
    except Exception as e:
        return {"error": str(e)}, 500

@app.route("/send_email_oauth", methods=["POST"])
def send_email_oauth():
    """Send email using Google OAuth2"""
    try:
        if not google_oauth_service.is_authenticated():
            return {"error": "Not authenticated with Google. Please sign in first."}, 401

        data = request.get_json()
        recipient_email = data.get('recipient_email')
        recipient_name = data.get('recipient_name', 'User')
        employee_id = data.get('employee_id')

        if not recipient_email or not employee_id:
            return {"error": "Recipient email and employee ID are required"}, 400

        # Get employee data
        df, _ = get_active_dataset()
        # Try both formats: simple number and zero-padded
        employee = df[df['ID'].astype(str) == str(employee_id)]

        if employee.empty:
            # Try zero-padded format
            normalized_id = str(employee_id).zfill(3)
            employee = df[df['ID'].astype(str).str.zfill(3) == normalized_id]

        if employee.empty:
            return {"error": f"Employee with ID {employee_id} not found"}, 404

        emp = employee.iloc[0]
        employee_data = {
            'ID': str(emp['ID']),
            'Name': str(emp['Name']),
            'Position': str(emp['Position']),
            'Company': str(emp['Company'])
        }

        # QR code path - use the actual ID from the dataset
        qr_filename = f"{emp['ID']}.png"
        qr_path = os.path.join(QR_FOLDER, qr_filename)

        if not os.path.exists(qr_path):
            return {"error": "QR code not found"}, 404

        # Send email
        success, message = google_oauth_service.send_email(
            recipient_email, recipient_name, qr_path, employee_data
        )

        if success:
            return {"success": True, "message": message}
        else:
            return {"error": message}, 400

    except Exception as e:
        print(f"OAuth email send error: {str(e)}")
        return {"error": f"Failed to send email: {str(e)}"}, 500

if __name__ == "__main__":
    def run_flask():
        app.run(debug=False, port=5000, use_reloader=False)
    flask_thread = threading.Thread(target=run_flask)
    flask_thread.daemon = True
    flask_thread.start()
    import time
    time.sleep(1)
    webview.create_window(
        "Smart QR ID Panel",
        "http://localhost:5000",
        width=1280,
        height=720,
        resizable=True,
        fullscreen=False,
        frameless=False
    )
    webview.start(gui='edgechromium')
