#!/usr/bin/env python3
"""
Test and fix for the HTTP 400 error in email configuration
"""

import requests
import json
import time
import threading
from flask import Flask
from app import app
from email_service import email_service

def start_flask_server():
    """Start Flask server in a separate thread"""
    app.run(debug=False, host='localhost', port=5001, use_reloader=False)

def test_http_400_scenario():
    """Test the exact scenario that causes HTTP 400"""
    print("🧪 Testing HTTP 400 Error Scenario")
    print("=" * 50)
    
    base_url = "http://localhost:5001"
    
    # Wait for server to start
    print("⏳ Waiting for Flask server to start...")
    time.sleep(3)
    
    # Test 1: Call test_email_config without any configuration (should fail with 400)
    print("\n1️⃣ Testing /test_email_config without configuration:")
    try:
        response = requests.post(
            f"{base_url}/test_email_config",
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 400:
            print("   ✅ Expected 400 error - credentials not configured")
        else:
            print("   ❌ Unexpected response")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Connection failed - server not running")
        return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    # Test 2: Update configuration first, then test
    print("\n2️⃣ Testing proper flow: update config then test:")
    
    # Step 2a: Update configuration
    config_data = {
        "smtp_server": "smtp.gmail.com",
        "smtp_port": 587,
        "use_tls": True,
        "sender_email": "<EMAIL>",
        "sender_password": "test_password",
        "sender_name": "Test System"
    }
    
    try:
        print("   📝 Updating email configuration...")
        response = requests.post(
            f"{base_url}/update_email_config",
            json=config_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        print(f"   Config Update Status: {response.status_code}")
        print(f"   Config Update Response: {response.text}")
        
        if response.status_code == 200:
            print("   ✅ Configuration updated successfully")
        else:
            print("   ❌ Failed to update configuration")
            return False
            
    except Exception as e:
        print(f"   ❌ Config update error: {e}")
        return False
    
    # Step 2b: Now test the configuration
    try:
        print("   🧪 Testing email configuration...")
        response = requests.post(
            f"{base_url}/test_email_config",
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        print(f"   Test Status: {response.status_code}")
        print(f"   Test Response: {response.text}")
        
        if response.status_code == 400:
            print("   ✅ Expected 400 - invalid credentials")
        elif response.status_code == 200:
            print("   ✅ Test successful (unexpected but good)")
        else:
            print("   ❌ Unexpected response")
            
    except Exception as e:
        print(f"   ❌ Test error: {e}")
        return False
    
    return True

def analyze_frontend_issue():
    """Analyze the frontend issue from the screenshot"""
    print("\n🔍 Frontend Issue Analysis")
    print("=" * 50)
    
    print("From the screenshot, the issue is:")
    print("1. User enters email: <EMAIL>")
    print("2. User enters password: ••••••••••••")
    print("3. User clicks 'Test Email' button")
    print("4. Frontend shows: 'Test failed: HTTP 400: BAD REQUEST'")
    print()
    print("Root cause analysis:")
    print("- The frontend calls testEmailConfig() function")
    print("- This function first calls /update_email_config")
    print("- Then immediately calls /test_email_config")
    print("- The /test_email_config endpoint checks if credentials exist")
    print("- If credentials are missing/invalid, it returns 400")
    print()
    print("The 400 error occurs because:")
    print("a) Credentials are not properly saved before testing, OR")
    print("b) The saved credentials are invalid (wrong password), OR")
    print("c) Gmail requires app-specific password, not regular password")

def suggest_fixes():
    """Suggest fixes for the HTTP 400 error"""
    print("\n🔧 Suggested Fixes")
    print("=" * 50)
    
    print("1. Frontend Fix:")
    print("   - Add better error handling in testEmailConfig()")
    print("   - Show specific error messages from server response")
    print("   - Add validation before sending requests")
    print()
    print("2. Backend Fix:")
    print("   - Improve error messages in /test_email_config")
    print("   - Add more detailed logging")
    print("   - Return specific error codes for different failure types")
    print()
    print("3. User Education:")
    print("   - Gmail requires App Passwords, not regular passwords")
    print("   - Guide users to generate app-specific passwords")
    print("   - Provide clear setup instructions")
    print()
    print("4. Configuration Fix:")
    print("   - Validate email format before saving")
    print("   - Test connection immediately after saving config")
    print("   - Provide real-time feedback during configuration")

def main():
    """Main test function"""
    print("🚀 Starting Flask server for testing...")
    
    # Start Flask server in background
    server_thread = threading.Thread(target=start_flask_server, daemon=True)
    server_thread.start()
    
    # Run tests
    success = test_http_400_scenario()
    
    # Analyze the issue
    analyze_frontend_issue()
    
    # Suggest fixes
    suggest_fixes()
    
    print(f"\n📊 Test Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
    print("\nPress Ctrl+C to stop the server and exit")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 Stopping server...")

if __name__ == "__main__":
    main()
