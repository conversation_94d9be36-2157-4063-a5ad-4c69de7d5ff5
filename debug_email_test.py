#!/usr/bin/env python3
"""
Debug script to test email functionality and identify the HTTP 400 error
"""

import requests
import json
from email_service import email_service

def test_email_service_directly():
    """Test the email service directly without Flask"""
    print("🔧 Testing EmailService directly...")
    print("-" * 50)
    
    # Test with empty credentials (should fail)
    print("1. Testing with empty credentials:")
    email_service.sender_email = ""
    email_service.sender_password = ""
    success, message = email_service.test_connection()
    print(f"   Result: success={success}, message='{message}'")
    
    # Test with invalid credentials (should fail)
    print("\n2. Testing with invalid credentials:")
    email_service.sender_email = "<EMAIL>"
    email_service.sender_password = "wrong_password"
    success, message = email_service.test_connection()
    print(f"   Result: success={success}, message='{message}'")
    
    # Test with valid format but non-existent email
    print("\n3. Testing with valid format but non-existent email:")
    email_service.sender_email = "<EMAIL>"
    email_service.sender_password = "some_password"
    success, message = email_service.test_connection()
    print(f"   Result: success={success}, message='{message}'")

def test_flask_endpoints():
    """Test Flask endpoints directly"""
    print("\n🌐 Testing Flask endpoints...")
    print("-" * 50)
    
    base_url = "http://localhost:5000"
    
    # Test update email config endpoint
    print("1. Testing /update_email_config endpoint:")
    config_data = {
        "smtp_server": "smtp.gmail.com",
        "smtp_port": 587,
        "use_tls": True,
        "sender_email": "<EMAIL>",
        "sender_password": "test_password",
        "sender_name": "Test System"
    }
    
    try:
        response = requests.post(
            f"{base_url}/update_email_config",
            json=config_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text}")
    except requests.exceptions.ConnectionError:
        print("   ❌ Connection failed - Flask server not running")
        return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
    
    # Test email config test endpoint
    print("\n2. Testing /test_email_config endpoint:")
    try:
        response = requests.post(
            f"{base_url}/test_email_config",
            headers={"Content-Type": "application/json"}
        )
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return True

def analyze_frontend_request():
    """Analyze what the frontend is sending"""
    print("\n📱 Analyzing frontend request pattern...")
    print("-" * 50)
    
    # Simulate the exact request the frontend makes
    print("Frontend sends this config data:")
    frontend_config = {
        "smtp_server": "smtp.gmail.com",
        "smtp_port": 587,
        "use_tls": True,
        "sender_email": "<EMAIL>",  # From the screenshot
        "sender_password": "••••••••••••",  # Masked in UI
        "sender_name": "ID QR System"
    }
    
    print(json.dumps(frontend_config, indent=2))
    
    print("\nThen it calls /test_email_config with no body:")
    print("POST /test_email_config")
    print("Headers: {'Content-Type': 'application/json'}")
    print("Body: (empty)")

def check_email_service_state():
    """Check current state of email service"""
    print("\n📊 Current EmailService state...")
    print("-" * 50)
    
    print(f"SMTP Server: {email_service.smtp_server}")
    print(f"SMTP Port: {email_service.smtp_port}")
    print(f"Use TLS: {email_service.use_tls}")
    print(f"Sender Email: {email_service.sender_email}")
    print(f"Sender Password: {'*' * len(email_service.sender_password) if email_service.sender_password else '(empty)'}")
    print(f"Sender Name: {email_service.sender_name}")

def main():
    """Main test function"""
    print("🧪 Email Functionality Debug Test")
    print("=" * 60)
    
    # Check current state
    check_email_service_state()
    
    # Test email service directly
    test_email_service_directly()
    
    # Analyze frontend pattern
    analyze_frontend_request()
    
    # Test Flask endpoints if server is running
    flask_running = test_flask_endpoints()
    
    print("\n📋 Summary:")
    print("-" * 50)
    print("The HTTP 400 error likely occurs because:")
    print("1. Email credentials are not configured when test is called")
    print("2. The frontend calls test before saving config")
    print("3. The test_email_config endpoint checks for credentials first")
    print("\nTo fix: Ensure config is saved before testing, or modify")
    print("the test endpoint to accept credentials in the request body.")

if __name__ == "__main__":
    main()
