#!/usr/bin/env python3
"""
Test the fixed email functionality to verify HTTP 400 error is resolved
"""

import unittest
import requests
import json
import time
import threading
from flask import Flask
from app import app
from email_service import email_service

class TestFixedEmailFunctionality(unittest.TestCase):
    """Test the fixed email functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Start Flask server for testing"""
        cls.server_thread = threading.Thread(
            target=lambda: app.run(debug=False, host='localhost', port=5002, use_reloader=False),
            daemon=True
        )
        cls.server_thread.start()
        time.sleep(2)  # Wait for server to start
        cls.base_url = "http://localhost:5002"
    
    def test_empty_credentials_error_message(self):
        """Test that empty credentials return proper error message"""
        response = requests.post(
            f"{self.base_url}/test_email_config",
            headers={"Content-Type": "application/json"}
        )
        
        self.assertEqual(response.status_code, 400)
        data = response.json()
        self.assertIn("error", data)
        self.assertIn("credentials not configured", data["error"])
    
    def test_invalid_credentials_error_message(self):
        """Test that invalid credentials return helpful error message"""
        # First update config with invalid credentials
        config_data = {
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "use_tls": True,
            "sender_email": "<EMAIL>",
            "sender_password": "wrong_password",
            "sender_name": "Test System"
        }
        
        response = requests.post(
            f"{self.base_url}/update_email_config",
            json=config_data,
            headers={"Content-Type": "application/json"}
        )
        self.assertEqual(response.status_code, 200)
        
        # Now test the configuration
        response = requests.post(
            f"{self.base_url}/test_email_config",
            headers={"Content-Type": "application/json"}
        )
        
        self.assertEqual(response.status_code, 400)
        data = response.json()
        self.assertIn("error", data)
        # Should contain helpful message about App Password
        self.assertTrue(
            "App Password" in data["error"] or 
            "Authentication failed" in data["error"] or
            "Invalid email credentials" in data["error"]
        )
    
    def test_config_update_success(self):
        """Test that configuration update works properly"""
        config_data = {
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "use_tls": True,
            "sender_email": "<EMAIL>",
            "sender_password": "test_password",
            "sender_name": "Test System"
        }
        
        response = requests.post(
            f"{self.base_url}/update_email_config",
            json=config_data,
            headers={"Content-Type": "application/json"}
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data["success"])
        self.assertIn("updated", data["message"])

def run_manual_test():
    """Run manual test to demonstrate the fix"""
    print("🧪 Testing Fixed Email Functionality")
    print("=" * 50)
    
    base_url = "http://localhost:5002"
    
    print("1️⃣ Testing empty credentials (should return helpful error):")
    try:
        response = requests.post(
            f"{base_url}/test_email_config",
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n2️⃣ Testing invalid credentials (should return helpful error):")
    # Update config first
    config_data = {
        "smtp_server": "smtp.gmail.com",
        "smtp_port": 587,
        "use_tls": True,
        "sender_email": "<EMAIL>",  # Real email from screenshot
        "sender_password": "wrong_password",      # Wrong password
        "sender_name": "ID QR System"
    }
    
    try:
        response = requests.post(
            f"{base_url}/update_email_config",
            json=config_data,
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        print(f"   Config Update Status: {response.status_code}")
        
        # Now test
        response = requests.post(
            f"{base_url}/test_email_config",
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        print(f"   Test Status: {response.status_code}")
        print(f"   Test Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")

def main():
    """Main function"""
    print("🚀 Starting Flask server for testing...")
    
    # Start server
    server_thread = threading.Thread(
        target=lambda: app.run(debug=False, host='localhost', port=5002, use_reloader=False),
        daemon=True
    )
    server_thread.start()
    time.sleep(3)
    
    # Run manual test
    run_manual_test()
    
    print("\n🧪 Running unit tests...")
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n✅ Testing complete!")
    print("\n📋 Summary of Fixes Applied:")
    print("1. ✅ Improved frontend error handling with specific messages")
    print("2. ✅ Added Gmail App Password instructions in UI")
    print("3. ✅ Enhanced backend error messages")
    print("4. ✅ Fixed test assertion error")
    print("5. ✅ Added helpful links and guidance")
    
    print("\n🎯 The HTTP 400 error should now show helpful messages like:")
    print("   'Invalid email credentials. For Gmail, please use an App Password'")
    print("   instead of just 'HTTP 400: BAD REQUEST'")

if __name__ == "__main__":
    main()
